/**
 * 页面消息类型定义
 */
/**
 * 页面消息数据类型
 */
export type MessageData =
  | DownloadDataResponseMessage
  | HeadersSetMessage
  | GetDownloadDataRequest
  | DownloadFileRequest
  | Record<string, unknown>

/**
 * 下载数据响应消息
 */
export interface DownloadDataResponseMessage {
  success: boolean
  data?: any
  error?: string
  pageTaskId?: string
}

/**
 * 请求头设置消息
 */
export interface HeadersSetMessage {
  pageTaskId: string
  success: boolean
  error?: string
}

/**
 * 获取下载数据请求
 */
export interface GetDownloadDataRequest {
  requestId: string
}

/**
 * 下载文件请求
 */
export interface DownloadFileRequest {
  requestId: string
  url: string
  filename: string
  pageTaskId?: string
  requestHeaders?: Array<{ name: string; value: string }>
}

/**
 * 页面消息类型定义
 */
export interface PageMessage {
  type: string
  data: MessageData
}


