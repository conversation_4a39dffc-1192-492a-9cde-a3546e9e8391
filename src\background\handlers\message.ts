// 消息处理器模块
// 处理来自内容脚本、popup和sidepanel的各种消息类型

import type {
  Message,
  FilteredRequestInfo,
  GetDownloadDataByIdPayload,
  DownloadFileWithHeadersPayload,
  SetRequestHeadersPayload,
  SendToTabPayload
} from "@/types/network"
import type { Response } from "@/types/network"
import { RequestMonitor } from "@/background/services/monitor"
import {
  BackgroundMessageType,
  PageToExtensionMessageType,
  ExtensionToPageMessageType
} from "@/types/network"
import type { DownloadManager } from "@/background/services/download"
import type { HeaderManager } from "@/background/services/header"
import type { SidepanelManager } from "@/background/services/sidepanel"

export class MessageHandler {
  private requestMonitor: RequestMonitor
  private downloadManager?: DownloadManager
  private headerManager?: HeaderManager
  private sidepanelManager?: SidepanelManager

  constructor(
    requestMonitor: RequestMonitor,
    downloadManager?: DownloadManager,
    headerManager?: HeaderManager,
    sidepanelManager?: SidepanelManager
  ) {
    this.requestMonitor = requestMonitor
    this.downloadManager = downloadManager
    this.headerManager = headerManager
    this.sidepanelManager = sidepanelManager
  }

  // 设置依赖模块（用于延迟注入）
  setDependencies(modules: {
    downloadManager?: DownloadManager
    headerManager?: HeaderManager
    sidepanelManager?: SidepanelManager
  }) {
    if (modules.downloadManager) this.downloadManager = modules.downloadManager
    if (modules.headerManager) this.headerManager = modules.headerManager
    if (modules.sidepanelManager) this.sidepanelManager = modules.sidepanelManager
  }

  // 主消息处理函数
  async handleMessage(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response: Response) => void
  ): Promise<void> {
    console.log("收到消息:", message)

    switch (message.type) {
      // 插件内部通信消息
      case BackgroundMessageType.GET_FILTERED_REQUESTS:
        this.handleGetFilteredRequests(message.tabId, sendResponse)
        break

      case BackgroundMessageType.CLEAR_FILTERED_REQUESTS:
        this.handleClearFilteredRequests(sendResponse)
        break

      case BackgroundMessageType.CLEAR_FILTERED_REQUESTS_BY_TAB:
        this.handleClearFilteredRequestsByTab(message.tabId, sendResponse)
        break

      case BackgroundMessageType.SET_REQUEST_HEADERS:
        if (this.headerManager) {
          const payload = message.payload as SetRequestHeadersPayload
          await this.headerManager.handleSetRequestHeaders(payload, sendResponse)
        } else {
          sendResponse({ success: false, error: "请求头管理器未初始化" })
        }
        break

      case BackgroundMessageType.TOGGLE_SIDEPANEL:
        if (this.sidepanelManager) {
          await this.sidepanelManager.handleToggleSidepanel(sendResponse)
        } else {
          sendResponse({ success: false, error: "侧边栏管理器未初始化" })
        }
        break

      case BackgroundMessageType.SEND_TO_TAB:
        const sendToTabPayload = message.payload as SendToTabPayload
        await this.handleSendToTab(sendToTabPayload, sendResponse, sender.tab?.id)
        break

      // 前端页面发送给插件的消息
      case PageToExtensionMessageType.GET_DOWNLOAD_DATA_BY_ID:
        if (this.downloadManager) {
          const getDataPayload = message.payload as GetDownloadDataByIdPayload
          await this.downloadManager.handleGetDownloadDataById(getDataPayload?.requestId, sendResponse)
        } else {
          sendResponse({ success: false, error: "下载管理器未初始化" })
        }
        break

      case PageToExtensionMessageType.DOWNLOAD_FILE_WITH_HEADERS:
        if (this.downloadManager) {
          const downloadPayload = message.payload as DownloadFileWithHeadersPayload
          const tabId = downloadPayload?.tabId || sender.tab?.id
          await this.downloadManager.handleDownloadFileWithHeaders(downloadPayload, sendResponse, tabId)
        } else {
          sendResponse({ success: false, error: "下载管理器未初始化" })
        }
        break



      // 处理内容脚本准备就绪消息（用于外部下载器页面通信）
      case ExtensionToPageMessageType.CONTENT_SCRIPT_READY:
        this.handleContentScriptReady(message, sendResponse)
        break

      default:
        console.warn("未知的消息类型:", message.type)
        sendResponse({
          success: false,
          error: "未知的消息类型"
        })
    }
  }

  // 处理内容脚本就绪消息
  private handleContentScriptReady(message: Message, sendResponse: (response: Response) => void) {
    console.log("内容脚本已准备就绪:", message.payload)
    sendResponse({
      success: true,
      message: "后台脚本已收到内容脚本就绪通知"
    })
  }

  // 处理向特定标签页发送消息
  private async handleSendToTab(
    payload: SendToTabPayload,
    sendResponse: (response: Response) => void,
    tabId?: number
  ) {
    try {
      if (!tabId) {
        sendResponse({ success: false, error: "缺少标签页ID" })
        return
      }

      console.log(`准备向标签页 ${tabId} 发送消息:`, payload.type)

      // 向特定标签页发送消息，并等待响应
      const response = await chrome.tabs.sendMessage(tabId, {
        type: payload.type,
        data: {
          ...payload.data,
          pageTaskId: payload.pageTaskId
        }
      })

      console.log(`标签页 ${tabId} 响应:`, response)
      sendResponse({ success: true, message: "消息已发送到标签页", data: response })
    } catch (error) {
      console.error("向标签页发送消息失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "发送消息失败"
      })
    }
  }

  // 处理获取过滤的请求列表
  private handleGetFilteredRequests(
    tabId: number | undefined,
    sendResponse: (response: Response) => void
  ) {
    try {
      let filteredRequests: FilteredRequestInfo[]

      if (tabId !== undefined) {
        // 获取指定标签页的过滤请求（移除数量限制）
        filteredRequests = this.requestMonitor.getFilteredRequestsByTab(tabId)
      } else {
        // 获取所有标签页的过滤请求（移除数量限制）
        filteredRequests = this.requestMonitor.getFilteredRequests()
      }

      sendResponse({
        success: true,
        data: {
          requests: filteredRequests,
          count: filteredRequests.length
        }
      })
    } catch (error) {
      console.error("获取过滤请求列表失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "获取过滤请求列表失败"
      })
    }
  }

  // 处理清空过滤请求列表
  private handleClearFilteredRequests(sendResponse: (response: Response) => void) {
    this.requestMonitor.clearFilteredRequests()
    sendResponse({
      success: true,
      message: "过滤请求列表已清空"
    })
  }

  // 处理清空指定标签页的过滤请求列表
  private handleClearFilteredRequestsByTab(
    tabId: number | undefined,
    sendResponse: (response: Response) => void
  ) {
    try {
      if (tabId === undefined) {
        sendResponse({
          success: false,
          error: "缺少标签页ID参数"
        })
        return
      }

      this.requestMonitor.clearFilteredRequestsByTab(tabId)
      sendResponse({
        success: true,
        message: `标签页 ${tabId} 的过滤请求列表已清空`
      })
    } catch (error) {
      console.error("清空标签页过滤请求失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "清空标签页过滤请求失败"
      })
    }
  }
}
