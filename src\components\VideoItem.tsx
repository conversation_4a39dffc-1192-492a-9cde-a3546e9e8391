import { useState, useRef, useEffect } from "react"
import type { VideoItemProps } from "@/types"
import Hls from 'hls.js';

export default function VideoItem({ video, showThumbnail = false, className = "", onPlayClick }: VideoItemProps) {
  const [copySuccess, setCopySuccess] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)

  // video元素引用
  const videoRef = useRef<HTMLVideoElement>(null);

  // 处理HLS实例生命周期
  useEffect(() => {
    if (showThumbnail && videoRef.current) {
      const videoElement = videoRef.current;
      let hls: Hls | null = null;

      // 检测M3U8格式
      const isM3u8 = video.url.includes('.m3u8') ||
        video.url.includes('application/vnd.apple.mpegurl') ||
        video.url.includes('application/x-mpegurl');

      if (isM3u8) {
        // M3U8流处理
        if (Hls.isSupported()) {
          hls = new Hls();
          hls.attachMedia(videoElement);
          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            // 可在此处控制自动播放
            // videoElement.play();
          });
          hls.loadSource(video.url);
        }
        // Safari原生HLS支持
        else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
          videoElement.src = video.url;
        }
      } else {
        // 标准媒体文件
        videoElement.src = video.url;
      }

      // 清理HLS实例，防止内存泄漏
      return () => {
        if (hls) {
          hls.destroy();
        }
      };
    }
  }, [video.url, showThumbnail]);

  // 复制视频链接
  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(video.url)
      setCopySuccess(true)
      // 2秒后重置状态
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 处理下载
  const handleDownload = async () => {
    if (isDownloading || !video.requestId) return

    setIsDownloading(true)
    try {
      // 检测M3U8格式
      const isM3u8 = video.url.includes('.m3u8') ||
        video.url.includes('application/vnd.apple.mpegurl') ||
        video.url.includes('application/x-mpegurl') ||
        video.ext?.toLowerCase() === 'm3u8';

      // 为M3U8格式添加特殊标识
      const downloadData = {
        ...video,
        filename: video.title, // 确保外部下载页面能够正确获取文件名
        isM3u8: isM3u8,
        downloadType: isM3u8 ? 'm3u8' : 'standard'
      };

      // 将视频数据存储到Chrome storage，供外部页面访问
      await chrome.storage.local.set({
        [`downloadData_${video.requestId}`]: downloadData
      })

      // 打开外部下载器页面（独立的 React 项目）
      const downloaderUrl = `http://localhost:3456/?requestId=${video.requestId}`
      window.open(downloaderUrl, '_blank')

      console.log(`外部下载器已打开 - ${isM3u8 ? 'M3U8流媒体' : '标准媒体'}下载`)
    } catch (error) {
      console.error('下载失败:', error)
    } finally {
      setIsDownloading(false)
    }
  }
  return (
    <div className={`flex flex-col justify-center items-start p-2 gap-2 w-full bg-white rounded-lg ${className}`}>
      {/* 标题行 */}
      <div className="flex flex-row items-center gap-1 w-full">
        {video.favIconUrl && (
          <div className="w-4 h-4 flex-shrink-0">
            <img
              src={video.favIconUrl}
              alt="平台图标"
              className="w-full h-full object-contain rounded-sm"
            />
          </div>
        )}
        <span className="text-xs font-medium text-gray-900 flex-1 truncate" title={video.title}>
          {video.title}
        </span>
      </div>

      {/* 信息和操作行 */}
      <div className="flex flex-row items-center gap-2 w-full">
        <span className="text-xs font-medium text-gray-500 flex-1" title={video.size}>
          {video.size}
        </span>

        <div className="flex flex-row items-center gap-1">
          {/* 复制按钮 */}
          <button
            className="flex justify-center items-center w-5 h-5 rounded-full hover:bg-gray-100"
            onClick={handleCopyUrl}
            title={copySuccess ? "复制成功!" : "复制视频链接"}
          >
            {copySuccess ? (
              // 复制成功图标 - 与状态按钮的完成图标保持一致
              <svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4.29324 9.99999C4.0974 10.0009 3.90902 9.90894 3.7684 9.74369L0.226464 5.57747C0.155867 5.49392 0.0995364 5.39434 0.0606889 5.2844C0.0218415 5.17446 0.00123773 5.05633 5.41344e-05 4.93674C-0.00233624 4.69522 0.0745209 4.46244 0.213718 4.28961C0.352915 4.11678 0.543049 4.01806 0.742294 4.01516C0.941538 4.01226 1.13357 4.10543 1.27615 4.27416L4.29624 7.82508L10.7233 0.258806C10.8661 0.0900745 11.0583 -0.00299449 11.2577 7.3515e-05C11.4571 0.00314152 11.6473 0.102095 11.7865 0.275166C11.9257 0.448236 12.0025 0.681246 11.9999 0.922937C11.9974 1.16463 11.9158 1.3952 11.773 1.56393L4.81809 9.74369C4.67747 9.90894 4.48909 10.0009 4.29324 9.99999Z" fill="#1A56DB" />
              </svg>
            ) : (
              // 复制图标
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.79187 0H7.875V3C7.875 3.6618 7.31437 4.2 6.625 4.2H3.5V8.4C3.5 9.0618 4.04187 9.6 4.70812 9.6H9.79187C10.4581 9.6 11 9.0618 11 8.4V1.2C11 0.5382 10.4581 0 9.79187 0Z" fill="#374151" />
                <path d="M6.625 3V0.0780001C6.32375 0.1608 6.04437 0.3084 5.81687 0.5274L4.04937 2.2242C3.82125 2.4426 3.6675 2.7108 3.58125 3H6.625Z" fill="#374151" />
                <path d="M8.5 10.2H7.25V10.8H2.25L2.24938 3.6H2.875V2.4H2.25C1.56062 2.4 1 2.9382 1 3.6V10.8C1 11.4618 1.54188 12 2.20813 12H7.27063C7.6125 12 7.93187 11.8698 8.16937 11.634C8.40687 11.3982 8.5 11.1312 8.5 10.7598C8.5 10.7454 8.5 10.2 8.5 10.2Z" fill="#374151" />
              </svg>
            )}
          </button>

          {/* 预览按钮 */}
          <button
            className="flex justify-center items-center w-5 h-5 rounded-full hover:bg-gray-100"
            onClick={onPlayClick}
            title="预览视频"
          >
            <svg width="10" height="12" viewBox="0 0 10 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9.38425 4.86054L1.88161 0.181834C1.6909 0.0636439 1.47446 0.000946845 1.25393 1.06406e-05C1.0334 -0.000925564 0.816498 0.0599318 0.624917 0.176499C0.434635 0.290494 0.276364 0.456694 0.166493 0.657891C0.0566217 0.859089 -0.000856526 1.08797 9.64675e-06 1.32084V10.6783C-0.000856526 10.9111 0.0566217 11.14 0.166493 11.3412C0.276364 11.5424 0.434635 11.7086 0.624917 11.8226C0.81643 11.9397 1.0335 12.0009 1.25425 12C1.47499 11.9991 1.6916 11.936 1.88223 11.8173L9.38549 7.13854C9.57241 7.02274 9.72756 6.85657 9.8354 6.65669C9.94324 6.4568 10 6.23019 10 5.99954C10 5.76889 9.94324 5.54229 9.8354 5.3424C9.72756 5.14251 9.57241 4.97635 9.38549 4.86054H9.38425Z" fill="#374151" />
            </svg>
          </button>

          {/* 下载按钮 */}
          <button
            className="flex justify-center items-center w-5 h-5 rounded-full hover:bg-gray-100"
            onClick={handleDownload}
            disabled={isDownloading}
            title={isDownloading ? "下载中..." : "下载文件"}
          >
            {isDownloading ? (
              // 下载中的加载图标
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-spin">
                <path d="M6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0ZM6 10.8C3.35786 10.8 1.2 8.64214 1.2 6C1.2 3.35786 3.35786 1.2 6 1.2C8.64214 1.2 10.8 3.35786 10.8 6C10.8 8.64214 8.64214 10.8 6 10.8Z" fill="#E5E7EB" />
                <path d="M6 0C7.5913 0 9.11742 0.632141 10.2426 1.75736C11.3679 2.88258 12 4.4087 12 6H10.8C10.8 4.72696 10.2943 3.50606 9.39411 2.60589C8.49394 1.70571 7.27304 1.2 6 1.2V0Z" fill="#1A56DB" />
              </svg>
            ) : (
              // 下载图标
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_410_2340)">
                  <path d="M8.8242 4.488C8.71168 4.37263 8.5591 4.30782 8.4 4.30782C8.2409 4.30782 8.08832 4.37263 7.9758 4.488L6.6 5.89908V0.615385C6.6 0.452174 6.53679 0.295649 6.42426 0.180242C6.31174 0.064835 6.15913 0 6 0C5.84087 0 5.68826 0.064835 5.57574 0.180242C5.46321 0.295649 5.4 0.452174 5.4 0.615385V5.89908L4.0242 4.488C3.96885 4.42922 3.90265 4.38234 3.82944 4.35009C3.75624 4.31784 3.67751 4.30086 3.59784 4.30015C3.51817 4.29944 3.43917 4.31501 3.36543 4.34596C3.29169 4.3769 3.2247 4.42259 3.16836 4.48037C3.11203 4.53815 3.06748 4.60686 3.03731 4.68249C3.00714 4.75812 2.99196 4.83915 2.99265 4.92086C2.99334 5.00257 3.00989 5.08332 3.04134 5.1584C3.07278 5.23348 3.11849 5.30139 3.1758 5.35815L5.5758 7.81969C5.63154 7.877 5.69775 7.92247 5.77064 7.95349C5.84353 7.98452 5.92168 8.00049 6.0006 8.00049C6.07952 8.00049 6.15767 7.98452 6.23056 7.95349C6.30345 7.92247 6.36967 7.877 6.4254 7.81969L8.8254 5.35815C8.93772 5.24259 9.0007 5.086 9.00047 4.92282C9.00025 4.75964 8.93684 4.60324 8.8242 4.488Z" fill="#1A56DB" />
                  <path d="M10.8 7.07692H9.27L7.485 8.90769C7.28999 9.10772 7.05848 9.26639 6.80369 9.37464C6.54889 9.4829 6.27579 9.53862 6 9.53862C5.72421 9.53862 5.45111 9.4829 5.19632 9.37464C4.94152 9.26639 4.71001 9.10772 4.515 8.90769L2.73 7.07692H1.2C0.88174 7.07692 0.576515 7.20659 0.351472 7.43741C0.126428 7.66822 0 7.98127 0 8.30769V10.7692C0 11.0957 0.126428 11.4087 0.351472 11.6395C0.576515 11.8703 0.88174 12 1.2 12H10.8C11.1183 12 11.4235 11.8703 11.6485 11.6395C11.8736 11.4087 12 11.0957 12 10.7692V8.30769C12 7.98127 11.8736 7.66822 11.6485 7.43741C11.4235 7.20659 11.1183 7.07692 10.8 7.07692ZM9.3 10.7692C9.122 10.7692 8.94799 10.7151 8.79999 10.6137C8.65198 10.5122 8.53663 10.3681 8.46851 10.1994C8.40039 10.0307 8.38257 9.84513 8.41729 9.66607C8.45202 9.48701 8.53774 9.32254 8.6636 9.19344C8.78947 9.06435 8.94984 8.97643 9.12442 8.94081C9.299 8.9052 9.47996 8.92348 9.64441 8.99334C9.80887 9.06321 9.94943 9.18152 10.0483 9.33332C10.1472 9.48512 10.2 9.66359 10.2 9.84615C10.2 10.091 10.1052 10.3258 9.9364 10.4989C9.76761 10.672 9.5387 10.7692 9.3 10.7692Z" fill="#1A56DB" />
                </g>
                <defs>
                  <clipPath id="clip0_410_2340">
                    <rect width="12" height="12" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* 缩略图区域 */}
      {showThumbnail && (
        <>
          <div className="flex flex-col items-center gap-2.5 w-full">
            {/* 使用video标签处理预览，使用hls.js处理m3u8文件 */}
            <video
              ref={videoRef}
              controls
              preload="metadata"
            />
          </div>
          {video.pageTitle && (
            <div className="text-xs font-medium text-gray-500 text-center w-full px-2">
              <p className="line-clamp-2" title={video.pageTitle}>
                {video.pageTitle}
              </p>
            </div>
          )}
        </>
      )}
    </div>
  )
} 